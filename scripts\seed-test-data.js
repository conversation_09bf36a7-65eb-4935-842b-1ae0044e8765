const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

// Helper function to generate random dates
const randomDate = (start, end) => {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))
}

// Helper function to generate random phone numbers
const randomPhone = () => {
  const prefixes = ['+998901', '+998902', '+998903', '+998904', '+998905', '+998906', '+998907', '+998908', '+998909']
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)]
  const number = Math.floor(Math.random() * 9000000) + 1000000
  return `${prefix}${number}`
}

// Sample data arrays
const firstNames = [
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>',
  '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'
]

con<PERSON> last<PERSON><PERSON><PERSON> = [
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>ov', 'Ismoilov',
  'Jalilov', 'Karimov', 'Latipov', 'Mahmudov', 'Nazarov', 'Otajonov', 'Pulatov', 'Qosimov',
  'Rahimov', 'Saidov', 'Toshmatov', 'Umarov', 'Valiyev', 'Yusupov', 'Zokirov', 'Alimov', 'Botirov'
]

const subjects = [
  'English Language', 'IELTS Preparation', 'SAT Preparation', 'Mathematics', 'Kids English',
  'Business English', 'Academic English', 'Conversation English', 'Grammar & Writing', 'Speaking & Listening'
]

const branches = ['Main Branch', 'Branch']
const levels = ['A1', 'A2', 'B1', 'B2', 'IELTS', 'SAT', 'MATH', 'KIDS']
const tiers = ['A_LEVEL', 'B_LEVEL', 'C_LEVEL', 'NEW']
const roles = ['ADMIN', 'TEACHER', 'CASHIER', 'ACADEMIC_MANAGER', 'RECEPTION', 'STUDENT']

// Schedule patterns
const schedules = [
  'Monday, Wednesday, Friday - 9:00-11:00',
  'Monday, Wednesday, Friday - 11:00-13:00',
  'Monday, Wednesday, Friday - 14:00-16:00',
  'Monday, Wednesday, Friday - 16:00-18:00',
  'Monday, Wednesday, Friday - 18:00-20:00',
  'Tuesday, Thursday, Saturday - 9:00-11:00',
  'Tuesday, Thursday, Saturday - 11:00-13:00',
  'Tuesday, Thursday, Saturday - 14:00-16:00',
  'Tuesday, Thursday, Saturday - 16:00-18:00',
  'Tuesday, Thursday, Saturday - 18:00-20:00'
]

async function generateUsers() {
  console.log('Creating users...')
  const users = []
  
  // Create admin user
  users.push({
    name: 'Parviz Adashov',
    phone: '+998906006299',
    email: '<EMAIL>',
    password: await bcrypt.hash('Parviz0106$', 10),
    role: 'ADMIN'
  })

  // Create role-specific users
  const roleUsers = [
    { role: 'CASHIER', count: 3 },
    { role: 'ACADEMIC_MANAGER', count: 2 },
    { role: 'RECEPTION', count: 2 },
    { role: 'TEACHER', count: 20 },
    { role: 'STUDENT', count: 50 }
  ]

  for (const roleUser of roleUsers) {
    for (let i = 0; i < roleUser.count; i++) {
      const firstName = firstNames[Math.floor(Math.random() * firstNames.length)]
      const lastName = lastNames[Math.floor(Math.random() * lastNames.length)]
      
      users.push({
        name: `${firstName} ${lastName}`,
        phone: randomPhone(),
        email: roleUser.role !== 'STUDENT' ? `${firstName.toLowerCase()}${i + 1}@innocrm.uz` : null,
        password: await bcrypt.hash('password123', 10),
        role: roleUser.role
      })
    }
  }

  const createdUsers = await prisma.user.createMany({
    data: users,
    skipDuplicates: true
  })

  console.log(`Created ${createdUsers.count} users`)
  return await prisma.user.findMany()
}

async function generateTeachers(users) {
  console.log('Creating teachers...')
  const teacherUsers = users.filter(user => user.role === 'TEACHER')
  const teachers = []

  // Distribute teachers across tiers (5 per tier)
  const tiersDistribution = [
    { tier: 'A_LEVEL', count: 5, minExp: 5, maxExp: 15 },
    { tier: 'B_LEVEL', count: 5, minExp: 3, maxExp: 8 },
    { tier: 'C_LEVEL', count: 5, minExp: 1, maxExp: 4 },
    { tier: 'NEW', count: 5, minExp: 0, maxExp: 2 }
  ]

  let userIndex = 0
  for (const tierInfo of tiersDistribution) {
    for (let i = 0; i < tierInfo.count; i++) {
      if (userIndex < teacherUsers.length) {
        teachers.push({
          userId: teacherUsers[userIndex].id,
          subject: subjects[Math.floor(Math.random() * subjects.length)],
          experience: Math.floor(Math.random() * (tierInfo.maxExp - tierInfo.minExp + 1)) + tierInfo.minExp,
          branch: branches[Math.floor(Math.random() * branches.length)],
          tier: tierInfo.tier,
          salary: Math.floor(Math.random() * 5000000) + 3000000 // 3M to 8M UZS
        })
        userIndex++
      }
    }
  }

  const createdTeachers = await prisma.teacher.createMany({
    data: teachers,
    skipDuplicates: true
  })

  console.log(`Created ${createdTeachers.count} teachers`)
  return await prisma.teacher.findMany({ include: { user: true } })
}

async function generateCourses() {
  console.log('Creating courses...')
  const courses = [
    { name: 'General English A1', level: 'A1', duration: 12, price: 800000, description: 'Beginner level English course' },
    { name: 'General English A2', level: 'A2', duration: 12, price: 900000, description: 'Elementary level English course' },
    { name: 'General English B1', level: 'B1', duration: 16, price: 1000000, description: 'Intermediate level English course' },
    { name: 'General English B2', level: 'B2', duration: 16, price: 1100000, description: 'Upper-intermediate level English course' },
    { name: 'IELTS Preparation', level: 'IELTS', duration: 20, price: 1500000, description: 'IELTS exam preparation course' },
    { name: 'SAT Preparation', level: 'SAT', duration: 24, price: 2000000, description: 'SAT exam preparation course' },
    { name: 'Mathematics', level: 'MATH', duration: 16, price: 1200000, description: 'Mathematics course for students' },
    { name: 'Kids English', level: 'KIDS', duration: 12, price: 700000, description: 'English course for children' }
  ]

  const createdCourses = await prisma.course.createMany({
    data: courses,
    skipDuplicates: true
  })

  console.log(`Created ${createdCourses.count} courses`)
  return await prisma.course.findMany()
}

async function main() {
  try {
    console.log('🌱 Starting comprehensive test data seeding...')
    
    // Clear existing data (optional - comment out if you want to keep existing data)
    console.log('Clearing existing data...')
    await prisma.activityLog.deleteMany()
    await prisma.announcement.deleteMany()
    await prisma.assessment.deleteMany()
    await prisma.cabinetSchedule.deleteMany()
    await prisma.cabinet.deleteMany()
    await prisma.attendance.deleteMany()
    await prisma.class.deleteMany()
    await prisma.payment.deleteMany()
    await prisma.enrollment.deleteMany()
    await prisma.student.deleteMany()
    await prisma.group.deleteMany()
    await prisma.teacher.deleteMany()
    await prisma.course.deleteMany()
    await prisma.lead.deleteMany()
    await prisma.user.deleteMany()
    
    // Generate base data
    const users = await generateUsers()
    const teachers = await generateTeachers(users)
    const courses = await generateCourses()
    
    console.log('✅ Phase 1 complete: Users, Teachers, and Courses created')
    console.log(`📊 Summary: ${users.length} users, ${teachers.length} teachers, ${courses.length} courses`)
    
  } catch (error) {
    console.error('❌ Error seeding data:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })

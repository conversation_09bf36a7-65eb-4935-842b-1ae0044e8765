const { PrismaClient } = require('@prisma/client')
const { execSync } = require('child_process')

const prisma = new PrismaClient()

async function generateDataSummary() {
  console.log('\n📊 COMPREHENSIVE TEST DATA SUMMARY')
  console.log('=====================================')
  
  try {
    // Users summary
    const userCounts = await prisma.user.groupBy({
      by: ['role'],
      _count: { role: true }
    })
    
    console.log('\n👥 USERS BY ROLE:')
    userCounts.forEach(role => {
      console.log(`   ${role.role}: ${role._count.role} users`)
    })
    
    // Teachers by tier
    const teacherTiers = await prisma.teacher.groupBy({
      by: ['tier'],
      _count: { tier: true }
    })
    
    console.log('\n🎓 TEACHERS BY TIER:')
    teacherTiers.forEach(tier => {
      console.log(`   ${tier.tier}: ${tier._count.tier} teachers`)
    })
    
    // Students by status
    const studentStatuses = await prisma.student.groupBy({
      by: ['status'],
      _count: { status: true }
    })
    
    console.log('\n📚 STUDENTS BY STATUS:')
    studentStatuses.forEach(status => {
      console.log(`   ${status.status}: ${status._count.status} students`)
    })
    
    // Payments by method and status
    const paymentMethods = await prisma.payment.groupBy({
      by: ['method'],
      _count: { method: true }
    })
    
    const paymentStatuses = await prisma.payment.groupBy({
      by: ['status'],
      _count: { status: true }
    })
    
    console.log('\n💳 PAYMENTS BY METHOD:')
    paymentMethods.forEach(method => {
      console.log(`   ${method.method}: ${method._count.method} payments`)
    })
    
    console.log('\n💰 PAYMENTS BY STATUS:')
    paymentStatuses.forEach(status => {
      console.log(`   ${status.status}: ${status._count.status} payments`)
    })
    
    // Groups summary
    const totalGroups = await prisma.group.count()
    const activeGroups = await prisma.group.count({ where: { isActive: true } })
    
    console.log('\n🏫 GROUPS:')
    console.log(`   Total Groups: ${totalGroups}`)
    console.log(`   Active Groups: ${activeGroups}`)
    console.log(`   Inactive Groups: ${totalGroups - activeGroups}`)
    
    // Leads by status
    const leadStatuses = await prisma.lead.groupBy({
      by: ['status'],
      _count: { status: true }
    })
    
    console.log('\n📞 LEADS BY STATUS:')
    leadStatuses.forEach(status => {
      console.log(`   ${status.status}: ${status._count.status} leads`)
    })
    
    // Enrollments
    const totalEnrollments = await prisma.enrollment.count()
    console.log(`\n📝 ENROLLMENTS: ${totalEnrollments} total`)
    
    // Assessments
    const totalAssessments = await prisma.assessment.count()
    console.log(`\n📋 ASSESSMENTS: ${totalAssessments} total`)
    
    // Cabinets
    const totalCabinets = await prisma.cabinet.count()
    const activeCabinets = await prisma.cabinet.count({ where: { isActive: true } })
    console.log(`\n🏢 CABINETS: ${totalCabinets} total (${activeCabinets} active)`)
    
    // Announcements
    const totalAnnouncements = await prisma.announcement.count()
    console.log(`\n📢 ANNOUNCEMENTS: ${totalAnnouncements} total`)
    
    console.log('\n=====================================')
    console.log('✅ Test data generation completed successfully!')
    
  } catch (error) {
    console.error('❌ Error generating summary:', error)
  }
}

async function runAllSeeds() {
  try {
    console.log('🚀 Starting comprehensive test data generation...')
    console.log('This will create realistic sample data for all CRM features.\n')
    
    // Run each seeding script in sequence
    console.log('Phase 1: Creating users, teachers, and courses...')
    execSync('node scripts/seed-test-data.js', { stdio: 'inherit' })
    
    console.log('\nPhase 2: Creating groups, students, enrollments, and payments...')
    execSync('node scripts/seed-test-data-part2.js', { stdio: 'inherit' })
    
    console.log('\nPhase 3: Creating leads, cabinets, assessments, and announcements...')
    execSync('node scripts/seed-test-data-part3.js', { stdio: 'inherit' })
    
    // Generate summary
    await generateDataSummary()
    
    console.log('\n🎯 TESTING INSTRUCTIONS')
    console.log('========================')
    console.log('\n1. PAYMENT SYSTEM TESTING:')
    console.log('   • Go to Payments page to see CASH and CARD payment records')
    console.log('   • Test payment recording with both methods')
    console.log('   • Verify payment statuses: PAID (green), DEBT (red), REFUNDED (blue)')
    console.log('   • Check payment history across different branches')
    
    console.log('\n2. TEACHER TIER SYSTEM TESTING:')
    console.log('   • Go to Teachers page to see tier badges (A-level: Gold, B-level: Blue, C-level: Green, New: Gray)')
    console.log('   • Create/edit teachers and test tier selection')
    console.log('   • Create new groups and verify teachers appear in tier priority order')
    console.log('   • Test group assignment with different tier teachers')
    
    console.log('\n3. GROUP ASSIGNMENT TESTING:')
    console.log('   • Create new groups and observe teacher sorting by tier')
    console.log('   • Verify A-level teachers appear first, then B-level, C-level, and New')
    console.log('   • Test capacity allocation based on teacher tiers')
    console.log('   • Check both M/W/F and T/T/S schedule patterns')
    
    console.log('\n4. STUDENT MANAGEMENT TESTING:')
    console.log('   • View students with different statuses (Active, Dropped, Paused, Completed)')
    console.log('   • Test dropped students in leads workflow')
    console.log('   • Verify student enrollment in appropriate groups')
    console.log('   • Check student payment histories')
    
    console.log('\n5. LEADS MANAGEMENT TESTING:')
    console.log('   • Test leads in different stages (NEW, CALLING, CALL_COMPLETED, etc.)')
    console.log('   • Verify call recording workflow with timers')
    console.log('   • Test group assignment after calls')
    console.log('   • Check archive system for completed leads')
    
    console.log('\n6. MULTI-BRANCH TESTING:')
    console.log('   • Switch between Main Branch and Branch')
    console.log('   • Verify data separation between branches')
    console.log('   • Test teacher and student filtering by branch')
    console.log('   • Check payment records per branch')
    
    console.log('\n7. ROLE-BASED ACCESS TESTING:')
    console.log('   • Login with different roles:')
    console.log('     - Admin: +************ / Parviz0106$')
    console.log('     - Other roles: Use generated accounts with password123')
    console.log('   • Test role-specific features and restrictions')
    console.log('   • Verify Cashier limited access to payments only')
    
    console.log('\n8. ASSESSMENT SYSTEM TESTING:')
    console.log('   • View student assessments with scores and results')
    console.log('   • Test group assessments assigned by teachers')
    console.log('   • Verify assessment types (Level Test, Progress Test, Final Exam)')
    
    console.log('\n📱 LOGIN CREDENTIALS:')
    console.log('   Admin: +************ / Parviz0106$')
    console.log('   Other users: Check generated phone numbers / password123')
    
    console.log('\n🌐 Application URL: http://localhost:3001')
    console.log('\n🎉 Happy Testing!')
    
  } catch (error) {
    console.error('❌ Error running seed scripts:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

// Check if bcryptjs is installed
try {
  require('bcryptjs')
} catch (error) {
  console.log('Installing bcryptjs dependency...')
  execSync('npm install bcryptjs', { stdio: 'inherit' })
}

runAllSeeds()

'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Switch } from '@/components/ui/switch'
import { Loader2, Users, BookOpen, User, Calendar, MapPin, Clock } from 'lucide-react'
import { useBranch } from '@/contexts/branch-context'

const groupSchema = z.object({
  name: z.string().min(2, 'Group name must be at least 2 characters'),
  courseId: z.string().min(1, 'Course is required'),
  teacherId: z.string().min(1, 'Teacher is required'),
  capacity: z.number().min(1, 'Capacity must be at least 1').max(50, 'Capacity cannot exceed 50'),
  schedule: z.string().min(1, 'Schedule is required'),
  room: z.string().optional(),
  cabinetId: z.string().optional(),
  branch: z.string().min(1, 'Branch is required'),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().min(1, 'End date is required'),
  isActive: z.boolean().default(true),
})

type GroupFormData = z.infer<typeof groupSchema>

interface GroupFormProps {
  initialData?: Partial<GroupFormData>
  onSubmit: (data: GroupFormData) => Promise<void>
  onCancel?: () => void
  isEditing?: boolean
}

interface Course {
  id: string
  name: string
  level: string
  duration: number
  price: number
}

interface Teacher {
  id: string
  subject: string
  branch: string
  tier?: string
  user: {
    id: string
    name: string
  }
}

interface Cabinet {
  id: string
  name: string
  number: string
  capacity: number
  branch: string
  isActive: boolean
}

const branches = [
  { value: 'Main Branch', label: 'Main Branch' },
  { value: 'Branch', label: 'Branch' },
]

const scheduleOptions = [
  'Monday, Wednesday, Friday - 9:00-11:00',
  'Monday, Wednesday, Friday - 11:00-13:00',
  'Monday, Wednesday, Friday - 14:00-16:00',
  'Monday, Wednesday, Friday - 16:00-18:00',
  'Monday, Wednesday, Friday - 18:00-20:00',
  'Tuesday, Thursday, Saturday - 9:00-11:00',
  'Tuesday, Thursday, Saturday - 11:00-13:00',
  'Tuesday, Thursday, Saturday - 14:00-16:00',
  'Tuesday, Thursday, Saturday - 16:00-18:00',
  'Tuesday, Thursday, Saturday - 18:00-20:00',
  'Daily - 9:00-10:30',
  'Daily - 10:30-12:00',
  'Daily - 14:00-15:30',
  'Daily - 15:30-17:00',
  'Daily - 17:00-18:30',
  'Daily - 18:30-20:00',
]

// Helper functions for teacher tier styling
const getTeacherTierStyle = (tier: string) => {
  switch (tier) {
    case 'A_LEVEL':
      return 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white font-bold'
    case 'B_LEVEL':
      return 'bg-gradient-to-r from-blue-400 to-blue-600 text-white font-medium'
    case 'C_LEVEL':
      return 'bg-gradient-to-r from-green-400 to-green-600 text-white'
    case 'NEW':
      return 'bg-gradient-to-r from-gray-400 to-gray-600 text-white'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getTeacherTierLabel = (tier: string) => {
  switch (tier) {
    case 'A_LEVEL':
      return 'A-Level'
    case 'B_LEVEL':
      return 'B-Level'
    case 'C_LEVEL':
      return 'C-Level'
    case 'NEW':
      return 'New'
    default:
      return 'Unknown'
  }
}

export default function GroupForm({ initialData, onSubmit, onCancel, isEditing = false }: GroupFormProps) {
  const { currentBranch } = useBranch()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [courses, setCourses] = useState<Course[]>([])
  const [teachers, setTeachers] = useState<Teacher[]>([])
  const [cabinets, setCabinets] = useState<Cabinet[]>([])
  const [filteredTeachers, setFilteredTeachers] = useState<Teacher[]>([])
  const [filteredCabinets, setFilteredCabinets] = useState<Cabinet[]>([])

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<GroupFormData>({
    resolver: zodResolver(groupSchema),
    defaultValues: {
      name: initialData?.name || '',
      courseId: initialData?.courseId || '',
      teacherId: initialData?.teacherId || '',
      capacity: initialData?.capacity || 15,
      schedule: initialData?.schedule || '',
      room: initialData?.room || '',
      cabinetId: initialData?.cabinetId || '',
      branch: initialData?.branch || currentBranch.name,
      startDate: initialData?.startDate || '',
      endDate: initialData?.endDate || '',
      isActive: initialData?.isActive ?? true,
    },
  })

  const selectedCourseId = watch('courseId')
  const selectedTeacherId = watch('teacherId')
  const selectedCabinetId = watch('cabinetId')
  const selectedBranch = watch('branch')
  const selectedSchedule = watch('schedule')
  const isActive = watch('isActive')

  // Set default branch when component mounts or currentBranch changes
  useEffect(() => {
    if (!initialData?.branch && currentBranch?.name) {
      setValue('branch', currentBranch.name)
    }
  }, [currentBranch, initialData?.branch, setValue])

  useEffect(() => {
    fetchCourses()
    fetchTeachers()
    fetchCabinets()
  }, [])

  useEffect(() => {
    // Filter teachers by branch and sort by tier priority
    const tierPriority: Record<string, number> = { 'A_LEVEL': 1, 'B_LEVEL': 2, 'C_LEVEL': 3, 'NEW': 4 }

    if (selectedBranch) {
      const filtered = teachers
        .filter(teacher => teacher.branch === selectedBranch)
        .sort((a, b) => {
          const aTier = tierPriority[a.tier || 'NEW'] || 4
          const bTier = tierPriority[b.tier || 'NEW'] || 4
          return aTier - bTier
        })
      setFilteredTeachers(filtered)

      // Reset teacher selection if current teacher is not in the filtered list
      if (selectedTeacherId && !filtered.find(t => t.id === selectedTeacherId)) {
        setValue('teacherId', '')
      }
    } else {
      const sorted = teachers.sort((a, b) => {
        const aTier = tierPriority[a.tier || 'NEW'] || 4
        const bTier = tierPriority[b.tier || 'NEW'] || 4
        return aTier - bTier
      })
      setFilteredTeachers(sorted)
    }
  }, [selectedBranch, teachers, selectedTeacherId, setValue])

  useEffect(() => {
    // Filter cabinets by branch
    if (selectedBranch) {
      const filtered = cabinets.filter(cabinet => cabinet.branch === selectedBranch && cabinet.isActive)
      setFilteredCabinets(filtered)

      // Reset cabinet selection if current cabinet is not in the filtered list
      if (selectedCabinetId && !filtered.find(c => c.id === selectedCabinetId)) {
        setValue('cabinetId', '')
      }
    } else {
      setFilteredCabinets(cabinets.filter(cabinet => cabinet.isActive))
    }
  }, [selectedBranch, cabinets, selectedCabinetId, setValue])

  const fetchCourses = async () => {
    try {
      const response = await fetch('/api/courses')
      const data = await response.json()
      setCourses(data.courses || [])
    } catch (error) {
      console.error('Error fetching courses:', error)
    }
  }

  const fetchTeachers = async () => {
    try {
      const response = await fetch('/api/teachers')
      const data = await response.json()
      setTeachers(data.teachers || [])
    } catch (error) {
      console.error('Error fetching teachers:', error)
    }
  }

  const fetchCabinets = async () => {
    try {
      const response = await fetch('/api/cabinets')
      const data = await response.json()
      setCabinets(data.cabinets || [])
    } catch (error) {
      console.error('Error fetching cabinets:', error)
    }
  }

  const handleFormSubmit = async (data: GroupFormData) => {
    setIsSubmitting(true)
    setError(null)

    try {
      await onSubmit(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  const selectedCourse = courses.find(c => c.id === selectedCourseId)

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Users className="h-5 w-5 mr-2" />
          {isEditing ? 'Edit Group' : 'Create New Group'}
        </CardTitle>
        <CardDescription>
          {isEditing ? 'Update group information' : 'Enter group details to create a new class group'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Basic Information Section */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 mb-4">
              <Users className="h-4 w-4 text-gray-500" />
              <h3 className="text-lg font-medium">Basic Information</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Group Name *</Label>
                <Input
                  id="name"
                  {...register('name')}
                  placeholder="e.g., A1-Morning-01"
                  className={errors.name ? 'border-red-500' : ''}
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="capacity">Capacity *</Label>
                <Input
                  id="capacity"
                  type="number"
                  min="1"
                  max="50"
                  {...register('capacity', { valueAsNumber: true })}
                  className={errors.capacity ? 'border-red-500' : ''}
                />
                {errors.capacity && (
                  <p className="text-sm text-red-500">{errors.capacity.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="branch">Branch *</Label>
                <Select
                  value={selectedBranch}
                  onValueChange={(value) => setValue('branch', value)}
                >
                  <SelectTrigger className={errors.branch ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select branch" />
                  </SelectTrigger>
                  <SelectContent>
                    {branches.map((branch) => (
                      <SelectItem key={branch.value} value={branch.value}>
                        {branch.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.branch && (
                  <p className="text-sm text-red-500">{errors.branch.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="room">Room</Label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="room"
                    {...register('room')}
                    placeholder="e.g., Room 101"
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="cabinetId">Cabinet</Label>
                <Select
                  value={selectedCabinetId}
                  onValueChange={(value) => setValue('cabinetId', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select cabinet" />
                  </SelectTrigger>
                  <SelectContent>
                    {filteredCabinets.map((cabinet) => (
                      <SelectItem key={cabinet.id} value={cabinet.id}>
                        {cabinet.name} (#{cabinet.number}) - Capacity: {cabinet.capacity}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {selectedBranch && filteredCabinets.length === 0 && (
                  <p className="text-sm text-yellow-600">No cabinets available for selected branch</p>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={isActive}
                onCheckedChange={(checked) => setValue('isActive', checked)}
              />
              <Label htmlFor="isActive">Active Group</Label>
            </div>
          </div>

          {/* Course and Teacher Section */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 mb-4">
              <BookOpen className="h-4 w-4 text-gray-500" />
              <h3 className="text-lg font-medium">Course & Teacher</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="courseId">Course *</Label>
                <Select
                  value={selectedCourseId}
                  onValueChange={(value) => setValue('courseId', value)}
                >
                  <SelectTrigger className={errors.courseId ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select course" />
                  </SelectTrigger>
                  <SelectContent>
                    {courses.map((course) => (
                      <SelectItem key={course.id} value={course.id}>
                        {course.name} - {course.level}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.courseId && (
                  <p className="text-sm text-red-500">{errors.courseId.message}</p>
                )}
                {selectedCourse && (
                  <p className="text-sm text-gray-600">
                    Duration: {selectedCourse.duration} weeks | Price: ${(selectedCourse.price / 12500).toFixed(0)}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="teacherId">Teacher *</Label>
                <Select
                  value={selectedTeacherId}
                  onValueChange={(value) => setValue('teacherId', value)}
                >
                  <SelectTrigger className={errors.teacherId ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select teacher" />
                  </SelectTrigger>
                  <SelectContent>
                    {filteredTeachers.map((teacher) => (
                      <SelectItem key={teacher.id} value={teacher.id}>
                        <div className="flex items-center justify-between w-full">
                          <span>{teacher.user.name} - {teacher.subject}</span>
                          <span className={`text-xs px-2 py-1 rounded-full ${getTeacherTierStyle(teacher.tier || 'NEW')}`}>
                            {getTeacherTierLabel(teacher.tier || 'NEW')}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.teacherId && (
                  <p className="text-sm text-red-500">{errors.teacherId.message}</p>
                )}
                {selectedBranch && filteredTeachers.length === 0 && (
                  <p className="text-sm text-yellow-600">No teachers available for selected branch</p>
                )}
              </div>
            </div>
          </div>

          {/* Schedule Section */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 mb-4">
              <Clock className="h-4 w-4 text-gray-500" />
              <h3 className="text-lg font-medium">Schedule & Duration</h3>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="schedule">Schedule *</Label>
                <Select
                  value={selectedSchedule}
                  onValueChange={(value) => setValue('schedule', value)}
                >
                  <SelectTrigger className={errors.schedule ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select schedule" />
                  </SelectTrigger>
                  <SelectContent>
                    {scheduleOptions.map((schedule) => (
                      <SelectItem key={schedule} value={schedule}>
                        {schedule}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.schedule && (
                  <p className="text-sm text-red-500">{errors.schedule.message}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="startDate">Start Date *</Label>
                  <div className="relative">
                    <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="startDate"
                      type="date"
                      {...register('startDate')}
                      className={`pl-10 ${errors.startDate ? 'border-red-500' : ''}`}
                    />
                  </div>
                  {errors.startDate && (
                    <p className="text-sm text-red-500">{errors.startDate.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="endDate">End Date *</Label>
                  <div className="relative">
                    <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="endDate"
                      type="date"
                      {...register('endDate')}
                      className={`pl-10 ${errors.endDate ? 'border-red-500' : ''}`}
                    />
                  </div>
                  {errors.endDate && (
                    <p className="text-sm text-red-500">{errors.endDate.message}</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditing ? 'Update Group' : 'Create Group'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

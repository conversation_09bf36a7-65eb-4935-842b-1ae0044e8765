'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, CreditCard, User, Calendar, DollarSign, FileText, Search, Check, ChevronsUpDown } from 'lucide-react'
import { useBranch } from '@/contexts/branch-context'
import { cn } from '@/lib/utils'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

const paymentSchema = z.object({
  studentId: z.string().min(1, 'Student is required'),
  amount: z.number().min(1, 'Amount must be greater than 0'),
  method: z.enum(['CASH', 'CARD']),
  status: z.enum(['PAID', 'DEBT', 'REFUNDED']).default('PAID'),
  description: z.string().optional(),
  learningStartDate: z.string().min(1, 'Learning start date is required'),
  learningEndDate: z.string().min(1, 'Learning end date is required'),
})

type PaymentFormData = z.infer<typeof paymentSchema>

interface PaymentFormProps {
  initialData?: Partial<PaymentFormData>
  onSubmit: (data: PaymentFormData) => Promise<void>
  onCancel?: () => void
  isEditing?: boolean
  preselectedStudentId?: string
}

interface Student {
  id: string
  user: {
    id: string
    name: string
    phone: string
  }
  enrollments: Array<{
    id: string
    startDate: string
    endDate?: string
    group: {
      course: {
        name: string
        price: number
      }
    }
  }>
  payments?: Array<{
    id: string
    amount: number
    status: string
    method: string
    description?: string
    createdAt: string
  }>
  unpaidAmount?: number
}

interface PaymentHistory {
  id: string
  amount: number
  status: string
  method: string
  description?: string
  createdAt: string
}

const paymentMethods = [
  { value: 'CASH', label: 'Cash', icon: '💵' },
  { value: 'CARD', label: 'Card', icon: '💳' },
]

const paymentStatuses = [
  { value: 'PAID', label: 'Paid', color: 'text-green-600' },
  { value: 'DEBT', label: 'Debt', color: 'text-red-600' },
  { value: 'REFUNDED', label: 'Refunded', color: 'text-blue-600' },
]

function PaymentForm({
  initialData,
  onSubmit,
  onCancel,
  isEditing = false,
  preselectedStudentId
}: PaymentFormProps) {
  const { currentBranch } = useBranch()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [students, setStudents] = useState<Student[]>([])
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null)
  const [paymentHistory, setPaymentHistory] = useState<PaymentHistory[]>([])
  const [isStudentComboboxOpen, setIsStudentComboboxOpen] = useState(false)
  const [studentSearchTerm, setStudentSearchTerm] = useState('')

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<PaymentFormData>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      studentId: preselectedStudentId || initialData?.studentId || '',
      amount: initialData?.amount || 750000,
      method: initialData?.method || 'CASH',
      status: initialData?.status || 'PAID',
      description: initialData?.description || '',
      learningStartDate: initialData?.learningStartDate || '',
      learningEndDate: initialData?.learningEndDate || '',
    },
  })

  const selectedStudentId = watch('studentId')
  const selectedMethod = watch('method')
  const selectedStatus = watch('status')
  const amount = watch('amount')
  const learningStartDate = watch('learningStartDate')
  const learningEndDate = watch('learningEndDate')

  useEffect(() => {
    if (currentBranch?.id) {
      fetchStudents()
    }
  }, [currentBranch?.id])

  useEffect(() => {
    if (selectedStudentId) {
      const student = students.find(s => s.id === selectedStudentId)
      setSelectedStudent(student || null)
      if (student) {
        fetchStudentPaymentHistory(selectedStudentId)
      }
    }
  }, [selectedStudentId, students])

  useEffect(() => {
    // Calculate suggested amount when learning dates change
    if (learningStartDate && learningEndDate && selectedStudent) {
      const suggestedAmount = calculatePaymentAmount(learningStartDate, learningEndDate, selectedStudent)
      if (suggestedAmount > 0) {
        setValue('amount', suggestedAmount)
      }
    }
  }, [learningStartDate, learningEndDate, selectedStudent, setValue])

  const fetchStudents = async () => {
    if (!currentBranch?.id) return

    try {
      const response = await fetch(`/api/students?branch=${currentBranch.id}`)
      const data = await response.json()
      setStudents(data.students || [])
    } catch (error) {
      console.error('Error fetching students:', error)
    }
  }

  const fetchStudentPaymentHistory = async (studentId: string) => {
    try {
      const response = await fetch(`/api/students/${studentId}/payments`)
      const data = await response.json()
      setPaymentHistory(data.payments || [])

      // Update selected student with unpaid amount
      if (selectedStudent) {
        setSelectedStudent({
          ...selectedStudent,
          unpaidAmount: data.unpaidAmount || 0
        })
      }
    } catch (error) {
      console.error('Error fetching payment history:', error)
    }
  }

  const calculatePaymentAmount = (startDate: string, endDate: string, student: Student): number => {
    if (!startDate || !endDate || !student.enrollments.length) return 0

    const start = new Date(startDate)
    const end = new Date(endDate)
    const diffTime = Math.abs(end.getTime() - start.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    // Calculate number of classes (13 classes per month = 13/30 classes per day)
    const classesPerDay = 13 / 30
    const totalClasses = Math.ceil(diffDays * classesPerDay)

    // Get course price per month
    const coursePrice = student.enrollments[0].group.course.price

    // Calculate proportional amount (price per class * number of classes)
    const pricePerClass = coursePrice / 13
    const calculatedAmount = Math.round(pricePerClass * totalClasses)

    // Add any existing debt
    const debt = student.unpaidAmount || 0

    return calculatedAmount + debt
  }

  const handleFormSubmit = async (data: PaymentFormData) => {
    setIsSubmitting(true)
    setError(null)

    try {
      await onSubmit(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  const filteredStudents = students.filter(student =>
    student.user.name.toLowerCase().includes(studentSearchTerm.toLowerCase()) ||
    student.user.phone.includes(studentSearchTerm)
  )

  const suggestedAmount = selectedStudent && learningStartDate && learningEndDate
    ? calculatePaymentAmount(learningStartDate, learningEndDate, selectedStudent)
    : selectedStudent?.enrollments[0]?.group.course.price || 0

  const totalDebt = selectedStudent?.unpaidAmount || 0

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <CreditCard className="h-5 w-5 mr-2" />
          {isEditing ? 'Edit Payment' : 'Record New Payment'}
        </CardTitle>
        <CardDescription>
          {isEditing ? 'Update payment information' : 'Enter payment details to record a new transaction'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Student Selection */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 mb-4">
              <User className="h-4 w-4 text-gray-500" />
              <h3 className="text-lg font-medium">Student Information</h3>
            </div>

            <div className="space-y-2">
              <Label htmlFor="studentId">Student *</Label>
              <Popover open={isStudentComboboxOpen} onOpenChange={setIsStudentComboboxOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={isStudentComboboxOpen}
                    className={cn(
                      "w-full justify-between",
                      errors.studentId ? 'border-red-500' : '',
                      !selectedStudentId && "text-muted-foreground"
                    )}
                    disabled={!!preselectedStudentId}
                  >
                    {selectedStudentId
                      ? students.find((student) => student.id === selectedStudentId)?.user.name
                      : "Search and select student..."}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0">
                  <Command>
                    <CommandInput
                      placeholder="Search students..."
                      value={studentSearchTerm}
                      onValueChange={setStudentSearchTerm}
                    />
                    <CommandEmpty>No student found.</CommandEmpty>
                    <CommandGroup className="max-h-64 overflow-auto">
                      {filteredStudents.map((student) => (
                        <CommandItem
                          key={student.id}
                          value={student.user.name}
                          onSelect={() => {
                            setValue('studentId', student.id)
                            setIsStudentComboboxOpen(false)
                            setStudentSearchTerm('')
                          }}
                        >
                          <Check
                            className={cn(
                              "mr-2 h-4 w-4",
                              selectedStudentId === student.id ? "opacity-100" : "opacity-0"
                            )}
                          />
                          <div className="flex flex-col">
                            <span className="font-medium">{student.user.name}</span>
                            <span className="text-sm text-gray-500">{student.user.phone}</span>
                          </div>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
              {errors.studentId && (
                <p className="text-sm text-red-500">{errors.studentId.message}</p>
              )}
              {selectedStudent && (
                <div className="bg-blue-50 p-3 rounded-lg">
                  <p className="text-sm font-medium">{selectedStudent.user.name}</p>
                  <p className="text-sm text-gray-600">{selectedStudent.user.phone}</p>
                  {selectedStudent.enrollments.length > 0 && (
                    <p className="text-sm text-gray-600">
                      Course: {selectedStudent.enrollments[0].group.course.name}
                    </p>
                  )}
                  {totalDebt > 0 && (
                    <p className="text-sm text-red-600 font-medium">
                      Outstanding Debt: {totalDebt.toLocaleString()} UZS
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Payment Details */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 mb-4">
              <DollarSign className="h-4 w-4 text-gray-500" />
              <h3 className="text-lg font-medium">Payment Details</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="amount">Amount (UZS) *</Label>
                <Input
                  id="amount"
                  type="number"
                  min="1"
                  step="1000"
                  {...register('amount', { valueAsNumber: true })}
                  className={errors.amount ? 'border-red-500' : ''}
                />
                {errors.amount && (
                  <p className="text-sm text-red-500">{errors.amount.message}</p>
                )}
                {suggestedAmount > 0 && (
                  <div className="space-y-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setValue('amount', suggestedAmount)}
                    >
                      Use calculated amount: {suggestedAmount.toLocaleString()} UZS
                    </Button>
                    {totalDebt > 0 && (
                      <p className="text-xs text-gray-600">
                        Includes {totalDebt.toLocaleString()} UZS outstanding debt
                      </p>
                    )}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="method">Payment Method *</Label>
                <Select
                  value={selectedMethod}
                  onValueChange={(value) => setValue('method', value as any)}
                >
                  <SelectTrigger className={errors.method ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select method" />
                  </SelectTrigger>
                  <SelectContent>
                    {paymentMethods.map((method) => (
                      <SelectItem key={method.value} value={method.value}>
                        {method.icon} {method.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.method && (
                  <p className="text-sm text-red-500">{errors.method.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status *</Label>
                <Select
                  value={selectedStatus}
                  onValueChange={(value) => setValue('status', value as any)}
                >
                  <SelectTrigger className={errors.status ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {paymentStatuses.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        <span className={status.color}>{status.label}</span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.status && (
                  <p className="text-sm text-red-500">{errors.status.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Learning Period */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 mb-4">
              <Calendar className="h-4 w-4 text-gray-500" />
              <h3 className="text-lg font-medium">Learning Period</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="learningStartDate">Learning Start Date *</Label>
                <Input
                  id="learningStartDate"
                  type="date"
                  {...register('learningStartDate')}
                  className={errors.learningStartDate ? 'border-red-500' : ''}
                />
                {errors.learningStartDate && (
                  <p className="text-sm text-red-500">{errors.learningStartDate.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="learningEndDate">Learning End Date *</Label>
                <Input
                  id="learningEndDate"
                  type="date"
                  {...register('learningEndDate')}
                  className={errors.learningEndDate ? 'border-red-500' : ''}
                />
                {errors.learningEndDate && (
                  <p className="text-sm text-red-500">{errors.learningEndDate.message}</p>
                )}
              </div>
            </div>

            {learningStartDate && learningEndDate && selectedStudent && (
              <div className="bg-green-50 p-3 rounded-lg">
                <p className="text-sm text-green-800">
                  Payment calculated based on learning period: {new Date(learningStartDate).toLocaleDateString()} - {new Date(learningEndDate).toLocaleDateString()}
                </p>
                <p className="text-xs text-green-600 mt-1">
                  Based on 13 classes per month schedule
                </p>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                {...register('description')}
                placeholder="Payment description or notes..."
                className="min-h-[80px]"
              />
            </div>
          </div>

          {/* Payment History */}
          {selectedStudent && paymentHistory.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center space-x-2 mb-4">
                <FileText className="h-4 w-4 text-gray-500" />
                <h3 className="text-lg font-medium">Payment History</h3>
              </div>

              <div className="bg-gray-50 rounded-lg p-4 max-h-64 overflow-y-auto">
                <div className="space-y-3">
                  {paymentHistory.map((payment) => (
                    <div key={payment.id} className="flex justify-between items-center p-3 bg-white rounded border">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{payment.amount.toLocaleString()} UZS</span>
                          <span className={cn(
                            "px-2 py-1 rounded-full text-xs font-medium",
                            payment.status === 'PAID' && "bg-green-100 text-green-800",
                            payment.status === 'DEBT' && "bg-red-100 text-red-800",
                            payment.status === 'REFUNDED' && "bg-blue-100 text-blue-800"
                          )}>
                            {payment.status}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600">{payment.method}</p>
                        {payment.description && (
                          <p className="text-xs text-gray-500">{payment.description}</p>
                        )}
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-600">
                          {new Date(payment.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Payment Summary */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium mb-2">Payment Summary</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Amount:</span>
                <span className="ml-2 font-medium">{amount.toLocaleString()} UZS</span>
              </div>
              <div>
                <span className="text-gray-600">Method:</span>
                <span className="ml-2 font-medium">
                  {paymentMethods.find(m => m.value === selectedMethod)?.label}
                </span>
              </div>
              <div>
                <span className="text-gray-600">Status:</span>
                <span className={`ml-2 font-medium ${paymentStatuses.find(s => s.value === selectedStatus)?.color}`}>
                  {paymentStatuses.find(s => s.value === selectedStatus)?.label}
                </span>
              </div>
              {selectedStudent && learningStartDate && learningEndDate && (
                <div>
                  <span className="text-gray-600">Period:</span>
                  <span className="ml-2 font-medium">
                    {new Date(learningStartDate).toLocaleDateString()} - {new Date(learningEndDate).toLocaleDateString()}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditing ? 'Update Payment' : 'Record Payment'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

export default PaymentForm
export { PaymentForm }

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const { generatePassword } = require('./import-students');

const prisma = new PrismaClient();

async function verifyStudents() {
  try {
    console.log('=== Student Import Verification ===\n');
    
    // Get total counts
    const totalUsers = await prisma.user.count({
      where: { role: 'STUDENT' }
    });
    
    const totalStudents = await prisma.student.count();
    
    console.log(`Total Student Users: ${totalUsers}`);
    console.log(`Total Student Profiles: ${totalStudents}`);
    
    // Check for users without student profiles
    const usersWithoutProfiles = await prisma.user.count({
      where: {
        role: 'STUDENT',
        studentProfile: null
      }
    });
    
    console.log(`Users without student profiles: ${usersWithoutProfiles}`);
    
    // Get sample students to verify
    const sampleStudents = await prisma.student.findMany({
      take: 5,
      include: {
        user: {
          select: {
            name: true,
            phone: true,
            password: true
          }
        }
      }
    });
    
    console.log('\n=== Sample Student Verification ===');
    
    for (const student of sampleStudents) {
      const expectedPassword = generatePassword(student.user.name, student.user.phone);
      const passwordMatches = await bcrypt.compare(expectedPassword, student.user.password);
      
      console.log(`\nStudent: ${student.user.name}`);
      console.log(`Phone: ${student.user.phone}`);
      console.log(`Expected Password: ${expectedPassword}`);
      console.log(`Password Correct: ${passwordMatches ? '✓' : '✗'}`);
      console.log(`Level: ${student.level}`);
      console.log(`Branch: ${student.branch}`);
      console.log(`Status: ${student.status}`);
    }
    
    // Check branch distribution
    const branchStats = await prisma.student.groupBy({
      by: ['branch'],
      _count: {
        id: true
      }
    });
    
    console.log('\n=== Branch Distribution ===');
    branchStats.forEach(stat => {
      console.log(`${stat.branch}: ${stat._count.id} students`);
    });
    
    // Check level distribution
    const levelStats = await prisma.student.groupBy({
      by: ['level'],
      _count: {
        id: true
      }
    });
    
    console.log('\n=== Level Distribution ===');
    levelStats.forEach(stat => {
      console.log(`${stat.level}: ${stat._count.id} students`);
    });
    
    // Check status distribution
    const statusStats = await prisma.student.groupBy({
      by: ['status'],
      _count: {
        id: true
      }
    });
    
    console.log('\n=== Status Distribution ===');
    statusStats.forEach(stat => {
      console.log(`${stat.status}: ${stat._count.id} students`);
    });
    
    // Test login for a sample student
    console.log('\n=== Login Test ===');
    if (sampleStudents.length > 0) {
      const testStudent = sampleStudents[0];
      const expectedPassword = generatePassword(testStudent.user.name, testStudent.user.phone);
      const passwordMatches = await bcrypt.compare(expectedPassword, testStudent.user.password);
      
      console.log(`Test Student: ${testStudent.user.name}`);
      console.log(`Login Phone: ${testStudent.user.phone}`);
      console.log(`Login Password: ${expectedPassword}`);
      console.log(`Can Login: ${passwordMatches ? '✓ YES' : '✗ NO'}`);
    }
    
    console.log('\n=== Verification Complete ===');
    
  } catch (error) {
    console.error('Verification failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Export for use in other scripts
module.exports = {
  verifyStudents
};

// Run verification if this script is executed directly
if (require.main === module) {
  verifyStudents();
}

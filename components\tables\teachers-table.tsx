"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import TeacherForm from "@/components/forms/teacher-form"
import { Search, Plus, Edit, Trash2, Download, Filter } from "lucide-react"

interface Teacher {
  id: string
  userId: string
  user: {
    name: string
    email: string | null
    phone: string
  }
  subject: string
  experience: number | null
  salary: number | null
  branch: string
  createdAt: string
  updatedAt: string
}

interface TeachersTableProps {
  initialData?: Teacher[]
}

export function TeachersTable({ initialData = [] }: TeachersTableProps) {
  const [teachers, setTeachers] = useState<Teacher[]>(initialData)
  const [filteredTeachers, setFilteredTeachers] = useState<Teacher[]>(initialData)
  const [searchTerm, setSearchTerm] = useState("")
  const [branchFilter, setBranchFilter] = useState("")
  const [subjectFilter, setSubjectFilter] = useState("")
  const [loading, setLoading] = useState(false)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingTeacher, setEditingTeacher] = useState<Teacher | null>(null)

  // Fetch teachers data
  const fetchTeachers = async () => {
    setLoading(true)
    try {
      const response = await fetch("/api/teachers")
      if (response.ok) {
        const data = await response.json()
        setTeachers(data)
        setFilteredTeachers(data)
      }
    } catch (error) {
      console.error("Error fetching teachers:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (initialData.length === 0) {
      fetchTeachers()
    }
  }, [initialData])

  // Filter teachers based on search and filters
  useEffect(() => {
    let filtered = teachers

    if (searchTerm) {
      filtered = filtered.filter(
        (teacher) =>
          teacher.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          teacher.user.phone.includes(searchTerm) ||
          teacher.subject.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (branchFilter) {
      filtered = filtered.filter((teacher) => teacher.branch === branchFilter)
    }

    if (subjectFilter) {
      filtered = filtered.filter((teacher) => teacher.subject === subjectFilter)
    }

    setFilteredTeachers(filtered)
  }, [teachers, searchTerm, branchFilter, subjectFilter])

  // Get unique branches and subjects for filters
  const uniqueBranches = [...new Set(teachers.map((teacher) => teacher.branch))]
  const uniqueSubjects = [...new Set(teachers.map((teacher) => teacher.subject))]

  // Handle teacher deletion
  const handleDelete = async (teacherId: string) => {
    if (!confirm("Are you sure you want to delete this teacher?")) return

    try {
      const response = await fetch(`/api/teachers/${teacherId}`, {
        method: "DELETE",
      })

      if (response.ok) {
        setTeachers(teachers.filter((teacher) => teacher.id !== teacherId))
      } else {
        alert("Failed to delete teacher")
      }
    } catch (error) {
      console.error("Error deleting teacher:", error)
      alert("Error deleting teacher")
    }
  }

  // Handle form submission
  const handleFormSubmit = async (data: any) => {
    try {
      // Submit the teacher data to the API
      const response = await fetch('/api/teachers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        throw new Error('Failed to save teacher')
      }

      setIsCreateDialogOpen(false)
      setEditingTeacher(null)
      fetchTeachers()
    } catch (error) {
      console.error('Error saving teacher:', error)
      throw error
    }
  }

  // Export to CSV
  const exportToCSV = () => {
    const headers = ["Name", "Phone", "Email", "Subject", "Experience", "Salary", "Branch", "Created"]
    const csvData = filteredTeachers.map((teacher) => [
      teacher.user.name,
      teacher.user.phone,
      teacher.user.email || "",
      teacher.subject,
      teacher.experience || "",
      teacher.salary || "",
      teacher.branch,
      new Date(teacher.createdAt).toLocaleDateString(),
    ])

    const csvContent = [headers, ...csvData]
      .map((row) => row.map((field) => `"${field}"`).join(","))
      .join("\n")

    const blob = new Blob([csvContent], { type: "text/csv" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `teachers-${new Date().toISOString().split("T")[0]}.csv`
    a.click()
    URL.revokeObjectURL(url)
  }

  return (
    <div className="space-y-4">
      {/* Header with actions */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
        <h2 className="text-2xl font-bold">Teachers</h2>
        <div className="flex gap-2">
          <Button onClick={exportToCSV} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Teacher
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Add New Teacher</DialogTitle>
              </DialogHeader>
              <TeacherForm onSubmit={handleFormSubmit} />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search teachers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <select
          value={branchFilter}
          onChange={(e) => setBranchFilter(e.target.value)}
          className="px-3 py-2 border rounded-md"
        >
          <option value="">All Branches</option>
          {uniqueBranches.map((branch) => (
            <option key={branch} value={branch}>
              {branch}
            </option>
          ))}
        </select>
        <select
          value={subjectFilter}
          onChange={(e) => setSubjectFilter(e.target.value)}
          className="px-3 py-2 border rounded-md"
        >
          <option value="">All Subjects</option>
          {uniqueSubjects.map((subject) => (
            <option key={subject} value={subject}>
              {subject}
            </option>
          ))}
        </select>
        {(branchFilter || subjectFilter || searchTerm) && (
          <Button
            variant="outline"
            onClick={() => {
              setBranchFilter("")
              setSubjectFilter("")
              setSearchTerm("")
            }}
          >
            Clear Filters
          </Button>
        )}
      </div>

      {/* Table */}
      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Contact</TableHead>
              <TableHead>Subject</TableHead>
              <TableHead>Experience</TableHead>
              <TableHead>Salary</TableHead>
              <TableHead>Branch</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  Loading teachers...
                </TableCell>
              </TableRow>
            ) : filteredTeachers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  No teachers found
                </TableCell>
              </TableRow>
            ) : (
              filteredTeachers.map((teacher) => (
                <TableRow key={teacher.id}>
                  <TableCell className="font-medium">
                    {teacher.user.name}
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div>{teacher.user.phone}</div>
                      {teacher.user.email && (
                        <div className="text-sm text-gray-500">
                          {teacher.user.email}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">{teacher.subject}</Badge>
                  </TableCell>
                  <TableCell>
                    {teacher.experience ? `${teacher.experience} years` : "N/A"}
                  </TableCell>
                  <TableCell>
                    {teacher.salary ? `${teacher.salary.toLocaleString()} UZS` : "N/A"}
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{teacher.branch}</Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setEditingTeacher(teacher)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle>Edit Teacher</DialogTitle>
                          </DialogHeader>
                          {editingTeacher && (
                            <TeacherForm
                              initialData={{
                                userId: editingTeacher.userId,
                                subject: editingTeacher.subject,
                                experience: editingTeacher.experience || undefined,
                                branch: editingTeacher.branch,
                              }}
                              onSubmit={handleFormSubmit}
                              isEditing={true}
                            />
                          )}
                        </DialogContent>
                      </Dialog>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(teacher.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Results count */}
      <div className="text-sm text-gray-500">
        Showing {filteredTeachers.length} of {teachers.length} teachers
      </div>
    </div>
  )
}

const fs = require('fs');
const path = require('path');

// Read and parse the JSON file
function parseStudentData() {
  try {
    const filePath = path.join(__dirname, '../data to add/Student (1).json');
    const rawData = fs.readFileSync(filePath, 'utf8');
    
    // The file appears to be a single line JSON array, let's parse it
    const students = JSON.parse(rawData);
    
    console.log(`Found ${students.length} students in the JSON file`);
    console.log('Sample student data:', students[0]);
    
    return students;
  } catch (error) {
    console.error('Error reading student data:', error);
    return [];
  }
}

// Generate password from first name and last 4 digits of phone
function generatePassword(name, phone) {
  // Extract first name (everything before the first space)
  const firstName = name.split(' ')[0];
  
  // Extract last 4 digits from phone number
  const phoneDigits = phone.replace(/\D/g, ''); // Remove non-digits
  const lastFourDigits = phoneDigits.slice(-4);
  
  return firstName + lastFourDigits;
}

// Transform student data for our system
function transformStudentData(students) {
  return students.map((student, index) => {
    const password = generatePassword(student.name, student.phone);
    
    return {
      // Original data
      originalId: student.id,
      name: student.name,
      phone: student.phone,
      joinDate: student.joinDate,
      paymentStatus: student.paymentStatus,
      
      // Generated data for our system
      password: password,
      email: null, // No email in source data
      role: 'STUDENT',
      
      // Student profile data
      level: 'IELTS', // Based on user preference for only IELTS level
      branch: index % 2 === 0 ? 'Main Branch' : 'Branch', // Alternate between branches
      status: 'ACTIVE', // All students start as ACTIVE
      emergencyContact: null,
      dateOfBirth: null,
      address: null
    };
  });
}

// Split students into chunks
function chunkArray(array, chunkSize) {
  const chunks = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
}

// Main function to analyze the data
function analyzeData() {
  console.log('=== Analyzing Student Data ===');
  
  const rawStudents = parseStudentData();
  if (rawStudents.length === 0) {
    console.log('No student data found');
    return;
  }
  
  const transformedStudents = transformStudentData(rawStudents);
  
  console.log('\n=== Data Analysis ===');
  console.log(`Total students: ${transformedStudents.length}`);
  
  // Check for duplicate phone numbers
  const phoneNumbers = transformedStudents.map(s => s.phone);
  const uniquePhones = new Set(phoneNumbers);
  console.log(`Unique phone numbers: ${uniquePhones.size}`);
  console.log(`Duplicate phones: ${phoneNumbers.length - uniquePhones.size}`);
  
  // Show sample transformed data
  console.log('\n=== Sample Transformed Data ===');
  console.log(JSON.stringify(transformedStudents.slice(0, 3), null, 2));
  
  // Split into chunks
  const chunks = chunkArray(transformedStudents, 20);
  console.log(`\n=== Chunks ===`);
  console.log(`Total chunks of 20: ${chunks.length}`);
  chunks.forEach((chunk, index) => {
    console.log(`Chunk ${index + 1}: ${chunk.length} students`);
  });
  
  return {
    students: transformedStudents,
    chunks: chunks,
    duplicatePhones: phoneNumbers.length - uniquePhones.size
  };
}

// Export for use in other scripts
module.exports = {
  parseStudentData,
  generatePassword,
  transformStudentData,
  chunkArray,
  analyzeData
};

// Run analysis if this script is executed directly
if (require.main === module) {
  analyzeData();
}

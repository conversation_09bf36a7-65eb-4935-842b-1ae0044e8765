const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

const leadNames = [
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
  '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'
];

const phoneNumbers = [
  '+998901234567', '+998902345678', '+998903456789', '+998904567890',
  '+998905678901', '+998906789012', '+998907890123', '+998908901234',
  '+998909012345', '+998910123456', '+998911234567', '+998912345678',
  '+998913456789', '+998914567890', '+998915678901', '+998916789012',
  '+998917890123', '+998918901234', '+998919012345', '+998920123456',
  '+998921234567', '+998922345678', '+998923456789', '+998924567890',
  '+998925678901', '+998926789012', '+998927890123', '+998928901234'
];

const statuses = ['NEW', 'CALLING', 'CALL_COMPLETED', 'GROUP_ASSIGNED', 'NOT_INTERESTED'];
const branches = ['Main Branch', 'Branch'];
const coursePreferences = ['IELTS', 'General English', 'Business English', 'Academic English', 'Conversation'];

async function addMoreLeads() {
  try {
    console.log('Adding more leads...');
    
    const leadsToAdd = [];
    
    for (let i = 0; i < leadNames.length; i++) {
      leadsToAdd.push({
        name: leadNames[i],
        phone: phoneNumbers[i],
        coursePreference: coursePreferences[Math.floor(Math.random() * coursePreferences.length)],
        status: statuses[Math.floor(Math.random() * statuses.length)],
        branch: branches[Math.floor(Math.random() * branches.length)],
        createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000), // Random date within last 30 days
        notes: `Lead generated from marketing campaign. Interested in English language learning.`
      });
    }
    
    // Add leads in batches to avoid conflicts
    for (const lead of leadsToAdd) {
      try {
        await prisma.lead.create({
          data: lead
        });
        console.log(`✅ Added lead: ${lead.name} (${lead.branch})`);
      } catch (error) {
        if (error.code === 'P2002') {
          console.log(`⚠️  Lead ${lead.name} already exists, skipping...`);
        } else {
          console.error(`❌ Error adding lead ${lead.name}:`, error.message);
        }
      }
    }
    
    // Final count
    const totalLeads = await prisma.lead.count();
    const leadsByBranch = await prisma.lead.groupBy({
      by: ['branch'],
      _count: { branch: true }
    });
    
    const leadsByStatus = await prisma.lead.groupBy({
      by: ['status'],
      _count: { status: true }
    });
    
    console.log('\n📊 Final Lead Statistics:');
    console.log(`Total Leads: ${totalLeads}`);
    console.log('By Branch:', leadsByBranch);
    console.log('By Status:', leadsByStatus);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addMoreLeads();

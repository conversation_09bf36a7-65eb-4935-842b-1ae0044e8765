const { importStudentsToDatabase } = require('./import-students-to-db');

// Helper script to continue importing students in batches
async function continueImport() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('Usage: node continue-import.js <batch_size>');
    console.log('Example: node continue-import.js 10  (imports next 10 chunks)');
    console.log('Example: node continue-import.js 50  (imports next 50 chunks)');
    return;
  }
  
  const batchSize = parseInt(args[0]) || 10;
  
  // Start from chunk 6 (since we've done 1-5)
  const startChunk = 6;
  const endChunk = startChunk + batchSize - 1;
  
  console.log(`Importing chunks ${startChunk} to ${endChunk} (${batchSize} chunks)`);
  console.log(`This will import approximately ${batchSize * 20} students`);
  
  await importStudentsToDatabase(startChunk, endChunk);
}

// Quick import functions for different batch sizes
async function importNext10() {
  await importStudentsToDatabase(6, 15);
}

async function importNext20() {
  await importStudentsToDatabase(6, 25);
}

async function importNext50() {
  await importStudentsToDatabase(6, 55);
}

async function importAll() {
  // Import all remaining students (from chunk 6 onwards)
  await importStudentsToDatabase(6);
}

module.exports = {
  continueImport,
  importNext10,
  importNext20,
  importNext50,
  importAll
};

// Run if called directly
if (require.main === module) {
  continueImport();
}

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const reportQuerySchema = z.object({
  type: z.enum(['student-progress', 'financial', 'attendance', 'teacher-performance']),
  format: z.enum(['json', 'csv', 'pdf']).optional().default('json'),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  groupId: z.string().optional(),
  teacherId: z.string().optional(),
  studentId: z.string().optional(),
  branch: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = reportQuerySchema.parse({
      type: searchParams.get('type'),
      format: searchParams.get('format'),
      startDate: searchParams.get('startDate'),
      endDate: searchParams.get('endDate'),
      groupId: searchParams.get('groupId'),
      teacherId: searchParams.get('teacherId'),
      studentId: searchParams.get('studentId'),
      branch: searchParams.get('branch'),
    })

    let reportData: any = {}

    switch (query.type) {
      case 'student-progress':
        reportData = await generateStudentProgressReport(query)
        break
      case 'financial':
        reportData = await generateFinancialReport(query)
        break
      case 'attendance':
        reportData = await generateAttendanceReport(query)
        break
      case 'teacher-performance':
        reportData = await generateTeacherPerformanceReport(query)
        break
      default:
        return NextResponse.json({ error: 'Invalid report type' }, { status: 400 })
    }

    if (query.format === 'csv') {
      return generateCSVResponse(reportData, query.type)
    } else if (query.format === 'pdf') {
      return generatePDFResponse(reportData, query.type)
    }

    return NextResponse.json(reportData)
  } catch (error) {
    console.error('Error generating report:', error)
    return NextResponse.json(
      { error: 'Failed to generate report' },
      { status: 500 }
    )
  }
}

async function generateStudentProgressReport(query: any) {
  const whereClause: any = {}
  
  if (query.startDate && query.endDate) {
    whereClause.createdAt = {
      gte: new Date(query.startDate),
      lte: new Date(query.endDate),
    }
  }

  if (query.studentId) {
    whereClause.studentId = query.studentId
  }

  if (query.groupId) {
    whereClause.group = { id: query.groupId }
  }

  const enrollments = await prisma.enrollment.findMany({
    where: whereClause,
    include: {
      student: {
        include: {
          user: true,
          attendances: {
            include: {
              class: {
                include: {
                  group: {
                    include: {
                      course: true,
                    },
                  },
                },
              },
            },
          },
        },
      },
      group: {
        include: {
          course: true,
          teacher: {
            include: {
              user: true,
            },
          },
        },
      },
    },
  })

  const progressData = enrollments.map((enrollment) => {
    const attendanceRecords = enrollment.student.attendances.filter(
      (attendance) => attendance.class.group.id === enrollment.groupId
    )
    
    const totalClasses = attendanceRecords.length
    const presentClasses = attendanceRecords.filter(
      (attendance) => attendance.status === 'PRESENT'
    ).length
    
    const attendanceRate = totalClasses > 0 ? (presentClasses / totalClasses) * 100 : 0

    return {
      studentName: enrollment.student.user.name,
      studentPhone: enrollment.student.user.phone,
      studentLevel: enrollment.student.level,
      courseName: enrollment.group.course.name,
      courseLevel: enrollment.group.course.level,
      groupName: enrollment.group.name,
      teacherName: enrollment.group.teacher.user.name,
      enrollmentStatus: enrollment.status,
      startDate: enrollment.startDate,
      endDate: enrollment.endDate,
      attendanceRate: Math.round(attendanceRate * 100) / 100,
      totalClasses,
      presentClasses,
      absentClasses: totalClasses - presentClasses,
    }
  })

  return {
    title: 'Student Progress Report',
    generatedAt: new Date().toISOString(),
    filters: query,
    summary: {
      totalStudents: progressData.length,
      averageAttendanceRate: progressData.length > 0 
        ? progressData.reduce((sum, student) => sum + student.attendanceRate, 0) / progressData.length
        : 0,
      activeEnrollments: progressData.filter(s => s.enrollmentStatus === 'ACTIVE').length,
      completedEnrollments: progressData.filter(s => s.enrollmentStatus === 'COMPLETED').length,
    },
    data: progressData,
  }
}

async function generateFinancialReport(query: any) {
  const whereClause: any = {}
  
  if (query.startDate && query.endDate) {
    whereClause.createdAt = {
      gte: new Date(query.startDate),
      lte: new Date(query.endDate),
    }
  }

  if (query.studentId) {
    whereClause.studentId = query.studentId
  }

  const payments = await prisma.payment.findMany({
    where: whereClause,
    include: {
      student: {
        include: {
          user: true,
          enrollments: {
            include: {
              group: {
                include: {
                  course: true,
                  teacher: {
                    include: {
                      user: true,
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  })

  const financialData = payments.map((payment) => ({
    paymentId: payment.id,
    studentName: payment.student.user.name,
    studentPhone: payment.student.user.phone,
    amount: payment.amount,
    method: payment.method,
    status: payment.status,
    description: payment.description,
    transactionId: payment.transactionId,
    dueDate: payment.dueDate,
    paidDate: payment.paidDate,
    createdAt: payment.createdAt,
    courses: payment.student.enrollments.map(e => e.group.course.name).join(', '),
  }))

  const summary = {
    totalPayments: payments.length,
    totalAmount: payments.reduce((sum, p) => sum + Number(p.amount), 0),
    paidAmount: payments
      .filter(p => p.status === 'PAID')
      .reduce((sum, p) => sum + Number(p.amount), 0),
    debtAmount: payments
      .filter(p => p.status === 'DEBT')
      .reduce((sum, p) => sum + Number(p.amount), 0),
    paymentMethods: Object.entries(
      payments.reduce((acc, p) => {
        acc[p.method] = (acc[p.method] || 0) + Number(p.amount)
        return acc
      }, {} as Record<string, number>)
    ).map(([method, amount]) => ({ method, amount })),
  }

  return {
    title: 'Financial Report',
    generatedAt: new Date().toISOString(),
    filters: query,
    summary,
    data: financialData,
  }
}

async function generateAttendanceReport(query: any) {
  const whereClause: any = {}
  
  if (query.startDate && query.endDate) {
    whereClause.class = {
      date: {
        gte: new Date(query.startDate),
        lte: new Date(query.endDate),
      },
    }
  }

  if (query.studentId) {
    whereClause.studentId = query.studentId
  }

  if (query.groupId) {
    whereClause.class = {
      ...whereClause.class,
      groupId: query.groupId,
    }
  }

  const attendances = await prisma.attendance.findMany({
    where: whereClause,
    include: {
      student: {
        include: {
          user: true,
        },
      },
      class: {
        include: {
          group: {
            include: {
              course: true,
            },
          },
          teacher: {
            include: {
              user: true,
            },
          },
        },
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  })

  const attendanceData = attendances.map((attendance) => ({
    studentName: attendance.student.user.name,
    studentPhone: attendance.student.user.phone,
    groupName: attendance.class.group.name,
    courseName: attendance.class.group.course.name,
    teacherName: attendance.class.teacher.user.name,
    classDate: attendance.class.date,
    classTopic: attendance.class.topic,
    status: attendance.status,
    notes: attendance.notes,
    createdAt: attendance.createdAt,
  }))

  const summary = {
    totalRecords: attendances.length,
    presentCount: attendances.filter(a => a.status === 'PRESENT').length,
    absentCount: attendances.filter(a => a.status === 'ABSENT').length,
    lateCount: attendances.filter(a => a.status === 'LATE').length,
    excusedCount: attendances.filter(a => a.status === 'EXCUSED').length,
    attendanceRate: attendances.length > 0 
      ? (attendances.filter(a => a.status === 'PRESENT' || a.status === 'LATE').length / attendances.length) * 100
      : 0,
  }

  return {
    title: 'Attendance Report',
    generatedAt: new Date().toISOString(),
    filters: query,
    summary,
    data: attendanceData,
  }
}

async function generateTeacherPerformanceReport(query: any) {
  const whereClause: any = {}
  
  if (query.teacherId) {
    whereClause.id = query.teacherId
  }

  if (query.branch) {
    whereClause.branch = query.branch
  }

  const teachers = await prisma.teacher.findMany({
    where: whereClause,
    include: {
      user: true,
      groups: {
        include: {
          course: true,
          enrollments: {
            include: {
              student: true,
            },
          },
          classes: {
            include: {
              attendances: true,
            },
          },
        },
      },
    },
  })

  const performanceData = teachers.map((teacher) => {
    const totalStudents = teacher.groups.reduce(
      (sum, group) => sum + group.enrollments.length,
      0
    )
    
    const totalClasses = teacher.groups.reduce(
      (sum, group) => sum + group.classes.length,
      0
    )
    
    const totalAttendances = teacher.groups.reduce(
      (sum, group) => sum + group.classes.reduce(
        (classSum, cls) => classSum + cls.attendances.length,
        0
      ),
      0
    )
    
    const presentAttendances = teacher.groups.reduce(
      (sum, group) => sum + group.classes.reduce(
        (classSum, cls) => classSum + cls.attendances.filter(
          a => a.status === 'PRESENT'
        ).length,
        0
      ),
      0
    )
    
    const attendanceRate = totalAttendances > 0 
      ? (presentAttendances / totalAttendances) * 100 
      : 0

    return {
      teacherName: teacher.user.name,
      teacherPhone: teacher.user.phone,
      subject: teacher.subject,
      experience: teacher.experience,
      salary: teacher.salary,
      branch: teacher.branch,
      totalGroups: teacher.groups.length,
      totalStudents,
      totalClasses,
      attendanceRate: Math.round(attendanceRate * 100) / 100,
      courses: teacher.groups.map(g => g.course.name).join(', '),
    }
  })

  return {
    title: 'Teacher Performance Report',
    generatedAt: new Date().toISOString(),
    filters: query,
    summary: {
      totalTeachers: performanceData.length,
      averageAttendanceRate: performanceData.length > 0
        ? performanceData.reduce((sum, t) => sum + t.attendanceRate, 0) / performanceData.length
        : 0,
      totalStudents: performanceData.reduce((sum, t) => sum + t.totalStudents, 0),
      totalClasses: performanceData.reduce((sum, t) => sum + t.totalClasses, 0),
    },
    data: performanceData,
  }
}

function generateCSVResponse(data: any, reportType: string) {
  // Convert data to CSV format
  const csvData = convertToCSV(data.data)
  
  return new NextResponse(csvData, {
    headers: {
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="${reportType}-report-${new Date().toISOString().split('T')[0]}.csv"`,
    },
  })
}

function generatePDFResponse(data: any, reportType: string) {
  // This would integrate with a PDF generation library like jsPDF or Puppeteer
  // For now, return a placeholder
  return NextResponse.json({ 
    message: 'PDF generation not implemented yet',
    downloadUrl: `/api/reports/pdf/${reportType}`,
  })
}

function convertToCSV(data: any[]): string {
  if (!data || data.length === 0) return ''
  
  const headers = Object.keys(data[0])
  const csvRows = [
    headers.join(','),
    ...data.map(row => 
      headers.map(header => {
        const value = row[header]
        // Escape commas and quotes in CSV
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`
        }
        return value
      }).join(',')
    )
  ]
  
  return csvRows.join('\n')
}

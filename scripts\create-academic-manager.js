const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createAcademicManager() {
  try {
    console.log('Creating Academic Manager user...')
    
    const hashedPassword = await bcrypt.hash('academic123', 10)
    
    const academicManager = await prisma.user.create({
      data: {
        phone: '+998903333333',
        password: hashedPassword,
        name: 'Academic Manager',
        email: '<EMAIL>',
        role: 'ACADEMIC_MANAGER'
      }
    })
    
    console.log('✅ Academic Manager user created successfully!')
    console.log('Phone:', academicManager.phone)
    console.log('Password: academic123')
    console.log('Role:', academicManager.role)
    console.log('Name:', academicManager.name)
    console.log('Email:', academicManager.email)
    
  } catch (error) {
    console.error('❌ Error creating Academic Manager user:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createAcademicManager()

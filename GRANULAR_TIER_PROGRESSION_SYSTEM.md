# Granular Teacher Tier Progression System

## Overview
Successfully implemented a **granular slot-based tier progression system** where teacher tier availability is determined independently for each unique combination of:
- **Course Level** (A1, A2, B1, B2, IELTS, etc.)
- **Schedule Days** (M/W/F or T/T/S)
- **Time Slot** (14:00-15:30, 16:00-17:30, etc.)

This ensures optimal utilization of high-tier teachers within specific teaching contexts while maintaining independent progression across different slots.

## 🎯 Core Logic: Slot-Specific 80% Rule

### **Granular Progression Example**
For **A1 M/W/F 14:00-15:30** specifically:
1. **Parviz <PERSON> (A-Level)** - Always available for this slot
2. **<PERSON><PERSON><PERSON> (B-Level)** - Only available when Parviz reaches 80% capacity in THIS SPECIFIC SLOT
3. **Ulmasov Og'abek (C-Level)** - Available when both Parviz AND Otabek reach 80% in THIS SLOT
4. **<PERSON><PERSON><PERSON><PERSON> (New)** - Available when all higher tiers reach 80% in THIS SLOT

### **Independent Slot Progression**
- **A1 M/W/F 14:00-15:30**: Parviz 60% → Only Parviz available
- **A1 T/T/S 16:00-17:30**: Parviz 90% → Parviz + Otabek available
- **B1 M/W/F 14:00-15:30**: Independent progression from A1 slots

Each slot maintains its own tier progression state!

## 🔧 Technical Implementation

### **API Enhancements**

#### **Groups API** (`/api/groups`)
```typescript
// Returns slot-specific tier analysis
{
  groups: [...],
  slotTierAnalysis: [
    {
      slotKey: "A1-MWF-14:00-15:30",
      courseLevel: "A1",
      days: "MWF", 
      time: "14:00-15:30",
      tierUtilization: [
        {
          tier: "A_LEVEL",
          utilizationRate: 75.5,
          groupCount: 2,
        },
        {
          tier: "B_LEVEL", 
          utilizationRate: 45.0,
          groupCount: 1,
        }
      ],
      availableTiers: ["A_LEVEL"], // B_LEVEL locked until A_LEVEL reaches 80%
      totalGroups: 3
    }
  ]
}
```

#### **Lead Assignment API** (`/api/leads/[id]/assign-group`)
```typescript
// Filters groups based on slot-specific 80% rule
const slotGroups = availableGroups.reduce((acc, group) => {
  const { time, days } = parseSchedule(group.schedule)
  const slotKey = `${group.course.level}-${days}-${time}`
  
  if (!acc[slotKey]) acc[slotKey] = { groups: [] }
  acc[slotKey].groups.push(group)
  return acc
}, {})

// Apply 80% rule within each slot independently
Object.entries(slotGroups).forEach(([slotKey, slotData]) => {
  // Calculate tier utilization for THIS SLOT only
  const tierUtilization = calculateSlotTierUtilization(slotData.groups)
  
  // Apply progression rule within this slot
  const availableInSlot = applySlotTierProgression(tierUtilization)
  
  finalAvailableGroups.push(...availableInSlot)
})
```

### **Schedule Parsing Logic**
```typescript
const parseSchedule = (schedule: string) => {
  const parsed = JSON.parse(schedule)
  const scheduleStr = Array.isArray(parsed) ? parsed.join(' ') : schedule
  
  // Extract time (e.g., "14:00-15:30")
  const timeMatch = scheduleStr.match(/(\d{1,2}):(\d{2})-(\d{1,2}):(\d{2})/)
  const time = timeMatch ? `${timeMatch[1]}:${timeMatch[2]}-${timeMatch[3]}:${timeMatch[4]}` : 'Unknown'
  
  // Extract days (M/W/F or T/T/S)
  const days = scheduleStr.toLowerCase().includes('monday') && 
              scheduleStr.toLowerCase().includes('wednesday') && 
              scheduleStr.toLowerCase().includes('friday') ? 'MWF' :
              scheduleStr.toLowerCase().includes('tuesday') && 
              scheduleStr.toLowerCase().includes('thursday') && 
              scheduleStr.toLowerCase().includes('saturday') ? 'TTS' : 'Other'
  
  return { time, days }
}
```

## 📊 Visual Interface Features

### **Groups Page Dashboard**
- **Slot-based Tier Cards**: Each unique slot shows independent tier progression
- **Granular Progress Bars**: Capacity utilization per tier within each slot
- **Availability Indicators**: Green (Available) / Gray (Locked) per slot
- **Explanatory Context**: Clear explanation of slot-specific progression

#### **Dashboard Example Display**
```
A1 - MWF - 14:00-15:30                    Available tiers: A-Level
├─ A-Level: 75.5% ████████░░ (2 groups) ✅ Available
├─ B-Level: 45.0% ████░░░░░░ (1 group)  🔒 Locked
└─ C-Level: 0.0%  ░░░░░░░░░░ (0 groups) 🔒 Locked

A1 - TTS - 16:00-17:30                     Available tiers: A-Level, B-Level  
├─ A-Level: 85.0% ████████▓░ (1 group)  ✅ Available
├─ B-Level: 60.0% ██████░░░░ (1 group)  ✅ Available (A-Level ≥80%)
└─ C-Level: 30.0% ███░░░░░░░ (1 group)  🔒 Locked
```

### **Lead Assignment Modal**
- **Slot Analysis Panel**: Shows tier availability for each relevant slot
- **Filtered Group Display**: Only shows groups from available tiers per slot
- **Context Information**: Explains why certain groups are/aren't available

#### **Modal Example Display**
```
Available Groups by Slot & Tier

A1 - MWF - 14:00-15:30                    2 available groups
├─ A-Level: 1/2 available (75.5% utilized)
├─ B-Level: 0/1 available (45.0% utilized) 
└─ C-Level: 0/0 available (0.0% utilized)

💡 Groups shown below are filtered based on slot-specific tier progression rules
```

## 🎯 Business Scenarios

### **Scenario 1: Early Stage Slot**
**A1 M/W/F 14:00-15:30**
- Parviz (A-Level): 18/23 students (78% capacity)
- Otabek (B-Level): 12/20 students (60% capacity)
- **Result**: Only Parviz's group available for new assignments
- **Reason**: Parviz hasn't reached 80% threshold yet

### **Scenario 2: Unlocked Slot**
**A1 M/W/F 14:00-15:30** (after progression)
- Parviz (A-Level): 19/23 students (83% capacity) ✅
- Otabek (B-Level): 12/20 students (60% capacity)
- **Result**: Both Parviz and Otabek groups available
- **Reason**: Parviz reached 80%, unlocking Otabek for this slot

### **Scenario 3: Independent Progression**
**Same time, different context:**
- **A1 T/T/S 14:00-15:30**: Parviz 90%, Otabek available ✅
- **A1 M/W/F 14:00-15:30**: Parviz 75%, Otabek locked 🔒
- **B1 M/W/F 14:00-15:30**: Independent progression entirely

## 🔄 Workflow Examples

### **Lead Assignment Process**
1. **Lead Profile**: Student needs A1 level, prefers M/W/F, available 14:00-15:30
2. **System Analysis**: Checks A1-MWF-14:00-15:30 slot specifically
3. **Tier Evaluation**:
   - Parviz (A-Level): 78% capacity → Available
   - Otabek (B-Level): 60% capacity → Locked (Parviz < 80%)
4. **Assignment Options**: Only Parviz's group shown
5. **Assignment**: Lead assigned to Parviz's group

### **Capacity Progression**
1. **Initial State**: Parviz 78% → Only A-Level available
2. **New Enrollment**: Parviz reaches 80.5%
3. **Tier Unlock**: System automatically makes Otabek available for this slot
4. **Next Assignment**: Both Parviz and Otabek groups shown for A1-MWF-14:00-15:30

## 📱 User Experience Benefits

### **For Administrators**
- **Granular Control**: Manage tier progression per specific teaching context
- **Optimal Utilization**: Ensure best teachers are fully utilized before expanding
- **Clear Visibility**: See exactly which slots have which tiers available

### **For Call Center Staff**
- **Context-Aware Assignment**: System shows only appropriate options for specific slots
- **Quality Assurance**: Automatically directed to highest available tier for each context
- **Simplified Decision**: No need to manually check tier availability rules

### **For Academic Managers**
- **Strategic Planning**: Understand capacity utilization across different contexts
- **Resource Optimization**: Identify which slots need attention or expansion
- **Quality Monitoring**: Track tier progression across all teaching contexts

## 🧪 Testing & Verification

### **Slot Independence Testing**
- ✅ A1-MWF-14:00 progression independent from A1-TTS-16:00
- ✅ Same teacher in different slots maintains separate capacity tracking
- ✅ Tier unlocking in one slot doesn't affect other slots

### **Capacity Calculation Testing**
- ✅ Utilization calculated correctly per slot
- ✅ 80% threshold properly enforced per slot
- ✅ Multiple groups in same slot aggregate correctly

### **Assignment Filtering Testing**
- ✅ Lead assignment shows only available tier groups per slot
- ✅ Slot-specific filtering works correctly
- ✅ Visual indicators match actual availability

## ✅ Implementation Status

**COMPLETE**: The granular tier progression system is fully operational with:
- ✅ Slot-specific tier progression (course level + days + time)
- ✅ Independent 80% capacity rules per slot
- ✅ Real-time slot-based tier utilization calculation
- ✅ Visual dashboard with granular progress indicators
- ✅ Filtered lead assignment based on slot-specific availability
- ✅ Comprehensive business logic for optimal teacher utilization

The system now ensures that Parviz (A-Level) must reach 80% capacity in the specific A1 M/W/F 14:00-15:30 slot before Otabek (B-Level) becomes available for that exact same slot, while maintaining completely independent progression for other slots and contexts.

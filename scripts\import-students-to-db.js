const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const { analyzeData } = require('./import-students');

const prisma = new PrismaClient();

// Hash password
async function hashPassword(password) {
  return await bcrypt.hash(password, 12);
}

// Remove duplicates by phone number (keep the first occurrence)
function removeDuplicatePhones(students) {
  const seen = new Set();
  const unique = [];
  
  for (const student of students) {
    if (!seen.has(student.phone)) {
      seen.add(student.phone);
      unique.push(student);
    }
  }
  
  console.log(`Removed ${students.length - unique.length} duplicate phone numbers`);
  return unique;
}

// Check if phone number already exists in database
async function checkExistingPhones(students) {
  const phones = students.map(s => s.phone);
  const existingUsers = await prisma.user.findMany({
    where: {
      phone: {
        in: phones
      }
    },
    select: {
      phone: true
    }
  });
  
  const existingPhones = new Set(existingUsers.map(u => u.phone));
  const newStudents = students.filter(s => !existingPhones.has(s.phone));
  
  console.log(`Found ${existingPhones.size} existing phone numbers in database`);
  console.log(`Will import ${newStudents.length} new students`);
  
  return newStudents;
}

// Import a single student
async function importStudent(studentData) {
  try {
    // Hash the password
    const hashedPassword = await hashPassword(studentData.password);
    
    // Create user first
    const user = await prisma.user.create({
      data: {
        name: studentData.name,
        phone: studentData.phone,
        email: studentData.email,
        role: 'STUDENT',
        password: hashedPassword,
      }
    });
    
    // Create student profile
    const student = await prisma.student.create({
      data: {
        userId: user.id,
        level: studentData.level,
        branch: studentData.branch,
        status: studentData.status === 'ACTIVE' ? 'ACTIVE' : 'ACTIVE', // All students start as ACTIVE
        emergencyContact: studentData.emergencyContact,
        dateOfBirth: studentData.dateOfBirth,
        address: studentData.address,
      }
    });
    
    return { user, student, success: true };
  } catch (error) {
    console.error(`Failed to import student ${studentData.name} (${studentData.phone}):`, error.message);
    return { success: false, error: error.message, studentData };
  }
}

// Import a chunk of students
async function importChunk(students, chunkNumber) {
  console.log(`\n=== Processing Chunk ${chunkNumber} (${students.length} students) ===`);
  
  const results = {
    success: 0,
    failed: 0,
    errors: []
  };
  
  for (let i = 0; i < students.length; i++) {
    const student = students[i];
    console.log(`Processing ${i + 1}/${students.length}: ${student.name} (${student.phone})`);
    
    const result = await importStudent(student);
    
    if (result.success) {
      results.success++;
      console.log(`✓ Successfully imported ${student.name}`);
    } else {
      results.failed++;
      results.errors.push({
        student: student.name,
        phone: student.phone,
        error: result.error
      });
      console.log(`✗ Failed to import ${student.name}: ${result.error}`);
    }
    
    // Small delay to avoid overwhelming the database
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log(`\nChunk ${chunkNumber} Results:`);
  console.log(`✓ Success: ${results.success}`);
  console.log(`✗ Failed: ${results.failed}`);
  
  return results;
}

// Main import function
async function importStudentsToDatabase(startChunk = 1, endChunk = null) {
  try {
    console.log('=== Starting Student Import Process ===');
    
    // Get and analyze data
    const { students, chunks } = analyzeData();
    
    // Remove duplicates
    const uniqueStudents = removeDuplicatePhones(students);
    console.log(`Processing ${uniqueStudents.length} unique students`);
    
    // Check existing phones in database
    const newStudents = await checkExistingPhones(uniqueStudents);
    
    if (newStudents.length === 0) {
      console.log('No new students to import!');
      return;
    }
    
    // Create new chunks from filtered data
    const newChunks = [];
    for (let i = 0; i < newStudents.length; i += 20) {
      newChunks.push(newStudents.slice(i, i + 20));
    }
    
    console.log(`Created ${newChunks.length} chunks from new students`);
    
    // Determine which chunks to process
    const actualEndChunk = endChunk || newChunks.length;
    const chunksToProcess = newChunks.slice(startChunk - 1, actualEndChunk);
    
    console.log(`Will process chunks ${startChunk} to ${Math.min(actualEndChunk, newChunks.length)}`);
    
    // Process chunks
    const overallResults = {
      totalSuccess: 0,
      totalFailed: 0,
      allErrors: []
    };
    
    for (let i = 0; i < chunksToProcess.length; i++) {
      const chunkNumber = startChunk + i;
      const chunk = chunksToProcess[i];
      
      const results = await importChunk(chunk, chunkNumber);
      
      overallResults.totalSuccess += results.success;
      overallResults.totalFailed += results.failed;
      overallResults.allErrors.push(...results.errors);
    }
    
    console.log('\n=== Final Results ===');
    console.log(`Total Success: ${overallResults.totalSuccess}`);
    console.log(`Total Failed: ${overallResults.totalFailed}`);
    
    if (overallResults.allErrors.length > 0) {
      console.log('\nErrors:');
      overallResults.allErrors.forEach(error => {
        console.log(`- ${error.student} (${error.phone}): ${error.error}`);
      });
    }
    
    return overallResults;
    
  } catch (error) {
    console.error('Import process failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Export functions
module.exports = {
  importStudentsToDatabase,
  importChunk,
  importStudent,
  checkExistingPhones,
  removeDuplicatePhones
};

// Run import if this script is executed directly
if (require.main === module) {
  const args = process.argv.slice(2);
  const startChunk = parseInt(args[0]) || 1;
  const endChunk = args[1] ? parseInt(args[1]) : null;
  
  console.log(`Starting import from chunk ${startChunk}${endChunk ? ` to ${endChunk}` : ''}`);
  importStudentsToDatabase(startChunk, endChunk);
}

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import * as z from 'zod'

const enrollmentSchema = z.object({
  studentId: z.string(),
  groupId: z.string(),
  status: z.enum(['ACTIVE', 'COMPLETED', 'DROPPED', 'SUSPENDED']).default('ACTIVE'),
  startDate: z.string(),
  endDate: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search')
    const status = searchParams.get('status')
    const groupId = searchParams.get('groupId')
    const studentId = searchParams.get('studentId')
    const branchId = searchParams.get('branch') || 'main'

    // Map branch ID to branch name for database query
    const branchName = branchId === 'main' ? 'Main Branch' : 'Branch'

    const where: any = {
      // Filter enrollments by branch through both student and group relationships
      AND: [
        { student: { branch: branchName } },
        { group: { branch: branchName } }
      ]
    }

    if (search) {
      where.OR = [
        {
          student: {
            branch: branchId,
            user: { name: { contains: search, mode: 'insensitive' } }
          }
        },
        {
          student: {
            branch: branchId,
            user: { phone: { contains: search } }
          }
        },
        {
          group: {
            branch: branchId,
            name: { contains: search, mode: 'insensitive' }
          }
        },
        {
          group: {
            branch: branchId,
            course: { name: { contains: search, mode: 'insensitive' } }
          }
        },
      ]
    }

    if (status) {
      where.status = status
    }

    if (groupId) {
      where.groupId = groupId
      // Ensure the group belongs to the current branch
      where.AND.push({ group: { id: groupId, branch: branchId } })
    }

    if (studentId) {
      where.studentId = studentId
      // Ensure the student belongs to the current branch
      where.AND.push({ student: { id: studentId, branch: branchId } })
    }

    const [enrollments, total] = await Promise.all([
      prisma.enrollment.findMany({
        where,
        include: {
          student: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  phone: true,
                  email: true,
                },
              },
            },
          },
          group: {
            include: {
              course: {
                select: {
                  id: true,
                  name: true,
                  level: true,
                  duration: true,
                  price: true,
                },
              },
              teacher: {
                include: {
                  user: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.enrollment.count({ where }),
    ])

    return NextResponse.json({
      enrollments,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching enrollments:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = enrollmentSchema.parse(body)

    // Check if student exists
    const student = await prisma.student.findUnique({
      where: { id: validatedData.studentId },
    })

    if (!student) {
      return NextResponse.json(
        { error: 'Student not found' },
        { status: 400 }
      )
    }

    // Check if group exists and has capacity
    const group = await prisma.group.findUnique({
      where: { id: validatedData.groupId },
      include: {
        _count: {
          select: {
            enrollments: {
              where: {
                status: 'ACTIVE',
              },
            },
          },
        },
      },
    })

    if (!group) {
      return NextResponse.json(
        { error: 'Group not found' },
        { status: 400 }
      )
    }

    if (!group.isActive) {
      return NextResponse.json(
        { error: 'Cannot enroll in inactive group' },
        { status: 400 }
      )
    }

    // Check group capacity
    if (group._count.enrollments >= group.capacity) {
      return NextResponse.json(
        { error: 'Group is at full capacity' },
        { status: 400 }
      )
    }

    // Check if student is already enrolled in this group
    const existingEnrollment = await prisma.enrollment.findUnique({
      where: {
        studentId_groupId: {
          studentId: validatedData.studentId,
          groupId: validatedData.groupId,
        },
      },
    })

    if (existingEnrollment) {
      return NextResponse.json(
        { error: 'Student is already enrolled in this group' },
        { status: 400 }
      )
    }

    const enrollment = await prisma.enrollment.create({
      data: {
        ...validatedData,
        startDate: new Date(validatedData.startDate),
        endDate: validatedData.endDate ? new Date(validatedData.endDate) : null,
      },
      include: {
        student: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                phone: true,
                email: true,
              },
            },
          },
        },
        group: {
          include: {
            course: {
              select: {
                name: true,
                level: true,
              },
            },
            teacher: {
              include: {
                user: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
    })

    return NextResponse.json(enrollment, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating enrollment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const { generatePassword } = require('./import-students');

const prisma = new PrismaClient();

async function checkOurStudents() {
  try {
    // Check for students with names from our recent import
    const recentStudents = await prisma.student.findMany({
      where: {
        user: {
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        }
      },
      include: {
        user: {
          select: {
            name: true,
            phone: true,
            password: true,
            createdAt: true
          }
        }
      },
      orderBy: {
        user: {
          createdAt: 'desc'
        }
      },
      take: 10
    });
    
    console.log('=== Recently Imported Students ===');
    console.log(`Found ${recentStudents.length} students imported in the last 24 hours\n`);
    
    for (const student of recentStudents) {
      const expectedPassword = generatePassword(student.user.name, student.user.phone);
      const passwordMatches = await bcrypt.compare(expectedPassword, student.user.password);
      
      console.log(`Name: ${student.user.name}`);
      console.log(`Phone: ${student.user.phone}`);
      console.log(`Expected Password: ${expectedPassword}`);
      console.log(`Password Correct: ${passwordMatches ? '✓' : '✗'}`);
      console.log(`Level: ${student.level}`);
      console.log(`Branch: ${student.branch}`);
      console.log(`Created: ${student.user.createdAt}`);
      console.log('---');
    }
    
    // Count total students imported today
    const todayCount = await prisma.student.count({
      where: {
        user: {
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
          }
        }
      }
    });
    
    console.log(`\nTotal students imported in last 24 hours: ${todayCount}`);
    
  } catch (error) {
    console.error('Error checking students:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkOurStudents();

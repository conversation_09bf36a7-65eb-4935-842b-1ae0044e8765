import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import * as z from 'zod'

const paymentSchema = z.object({
  studentId: z.string(),
  amount: z.number().min(0),
  method: z.enum(['CASH', 'CARD']),
  status: z.enum(['PAID', 'DEBT', 'REFUNDED']).default('PAID'),
  description: z.string().optional(),
  learningStartDate: z.string().optional(),
  learningEndDate: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search')
    const status = searchParams.get('status')
    const method = searchParams.get('method')
    const studentId = searchParams.get('studentId')
    const branchId = searchParams.get('branch') || 'main'

    // Map branch ID to branch name for database query
    const branchName = branchId === 'main' ? 'Main Branch' : 'Branch'

    const where: any = {
      // Filter payments by branch through student relationship
      student: {
        branch: branchName
      }
    }

    if (search) {
      where.OR = [
        {
          student: {
            branch: branchId,
            user: { name: { contains: search, mode: 'insensitive' } }
          }
        },
        {
          student: {
            branch: branchId,
            user: { phone: { contains: search } }
          }
        },
        { transactionId: { contains: search, mode: 'insensitive' } },
      ]
    }

    if (status) {
      where.status = status
    }

    if (method) {
      where.method = method
    }

    if (studentId) {
      where.studentId = studentId
      // Ensure the student belongs to the current branch
      where.student.id = studentId
    }

    const [payments, total] = await Promise.all([
      prisma.payment.findMany({
        where,
        include: {
          student: {
            include: {
              user: {
                select: {
                  name: true,
                  phone: true,
                },
              },
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.payment.count({ where }),
    ])

    return NextResponse.json({
      payments,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching payments:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = paymentSchema.parse(body)

    const payment = await prisma.payment.create({
      data: {
        studentId: validatedData.studentId,
        amount: validatedData.amount,
        method: validatedData.method,
        status: validatedData.status,
        description: validatedData.description,
        // Store learning period in description if provided
        ...(validatedData.learningStartDate && validatedData.learningEndDate && {
          description: `${validatedData.description || ''} (Learning period: ${validatedData.learningStartDate} to ${validatedData.learningEndDate})`.trim()
        }),
      },
      include: {
        student: {
          include: {
            user: {
              select: {
                name: true,
                phone: true,
              },
            },
          },
        },
      },
    })

    return NextResponse.json(payment, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating payment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
